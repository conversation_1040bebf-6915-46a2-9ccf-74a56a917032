from fpdf import FPDF
import os

class MatchrapportPDF(FPDF):
    def header(self):
        self.set_font("DejaVu", "B", 14)
        self.cell(0, 10, "Matchrapport", ln=True, align="C")
        self.ln(5)

    def footer(self):
        self.set_y(-15)
        self.set_font("DejaVu", "I", 8)
        self.cell(0, 10, f"Pagina {self.page_no()}", align="C")

    def add_text(self, text):
        self.set_font("DejaVu", "", 11)
        for line in text.splitlines():
            if line.startswith("### "):
                self.set_font("DejaVu", "B", 14)
                self.multi_cell(0, 10, line.replace("### ", "").strip())
                self.set_font("DejaVu", "", 11)
            elif line.startswith("#### "):
                self.set_font("DejaVu", "B", 12)
                self.multi_cell(0, 8, line.replace("#### ", "").strip())
                self.set_font("DejaVu", "", 11)
            else:
                self.multi_cell(0, 8, line)
        self.ln(4)

def convert_text_to_pdf(text, filename):
    output_dir = "rapportage"
    os.makedirs(output_dir, exist_ok=True)

    pdf = MatchrapportPDF()
    font_path = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Pas aan als je elders het font opslaat
    pdf.add_font("DejaVu", "", font_path, uni=True)
    pdf.add_font("DejaVu", "B", font_path, uni=True)
    pdf.add_font("DejaVu", "I", font_path, uni=True)
    pdf.set_auto_page_break(auto=True, margin=15)
    pdf.add_page()
    pdf.add_text(text)

    output_path = os.path.join(output_dir, filename)
    pdf.output(output_path)
    print(f"✅ PDF opgeslagen op: {output_path}")

