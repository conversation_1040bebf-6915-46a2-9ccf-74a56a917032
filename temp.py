import requests
from bs4 import BeautifulSoup

def haal_html_op(url):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()  # Raise error als niet 200
    return response.text

def extract_vacature_info(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')

    def get_section_text(section_title):
        section = soup.find('h6', string=lambda text: text and section_title.lower() in text.lower())
        if not section:
            return None
        content = ""
        for sibling in section.find_next_siblings():
            if sibling.name == 'h6':
                break
            content += sibling.get_text(separator="\n", strip=True) + "\n"
        return content.strip()
        
    bedrijfsomschrijving = get_section_text("bedrijfsomschrijving")
    functieomschrijving = get_section_text("functieomschrijving")
    functie_eisen = get_section_text("functie-eisen")

    samengestelde_tekst = f"""
### Bedrijfsomschrijving:

{bedrijfsomschrijving}

### Functieomschrijving:

{functieomschrijving}

### Functie-eisen:

{functie_eisen}"""

        
    return samengestelde_tekst.strip()
    
if __name__ == "__main__":
    try:
        html = haal_html_op("https://www.talentenscout.com/vacature.php?vacID=115")
        info = extract_vacature_info(html)
        print(info)
    except Exception as e:
        print(f"❌ Fout bij verwerken van de URL: {e}")
