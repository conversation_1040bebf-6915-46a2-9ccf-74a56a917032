#!/usr/bin/env python3
"""
Test script voor retry logic van stuur_prompt_naar_groq
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Laad environment variabelen
dotenv_path = Path(__file__).parent / ".env"
load_dotenv(dotenv_path=dotenv_path)

# Import de functie
from utils import stuur_prompt_naar_groq, lees_prompt

def test_retry_logic():
    print("🧪 Test retry logic voor stuur_prompt_naar_groq")
    print("=" * 50)
    
    # Check of GROQ_API_KEY bestaat
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ GROQ_API_KEY niet gevonden in .env bestand!")
        return
    else:
        print(f"✅ GROQ_API_KEY gevonden (lengte: {len(api_key)})")
    
    # Test met een eenvoudige prompt
    try:
        print("\n🔄 Testing retry logic met eenvoudige prompt...")
        prompt = "Geef een korte samenvatting van deze tekst:"
        html = "Dit is een test tekst voor de retry functionaliteit."
        
        result = stuur_prompt_naar_groq(prompt, html)
        
        print(f"\n✅ Test succesvol!")
        print(f"Resultaat lengte: {len(result)}")
        print(f"Resultaat: {result[:200]}...")
        
    except Exception as e:
        print(f"\n❌ Test gefaald: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_calls():
    """Test meerdere calls snel achter elkaar om rate limiting te triggeren"""
    print("\n🔄 Test meerdere calls voor rate limiting...")
    
    for i in range(3):
        try:
            print(f"\n📞 Call {i+1}/3...")
            prompt = f"Test call nummer {i+1}. Geef een kort antwoord."
            html = f"Test data voor call {i+1}"
            
            result = stuur_prompt_naar_groq(prompt, html)
            print(f"✅ Call {i+1} succesvol: {len(result)} karakters")
            
        except Exception as e:
            print(f"❌ Call {i+1} gefaald: {e}")

def main():
    test_retry_logic()
    test_multiple_calls()

if __name__ == "__main__":
    main()
