#!/usr/bin/env python3
"""
Debug script voor vacature_from_url functie
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Laad environment variabelen
dotenv_path = Path(__file__).parent / ".env"
load_dotenv(dotenv_path=dotenv_path)

# Import de functie
from utils import test_vacature_from_url, vacature_from_url

def main():
    print("🔧 Debug script voor vacature_from_url")
    print("=" * 50)
    
    # Check of GROQ_API_KEY bestaat
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ GROQ_API_KEY niet gevonden in .env bestand!")
        return
    else:
        print(f"✅ GROQ_API_KEY gevonden (lengte: {len(api_key)})")
    
    # Test met vacature ID 115
    print("\n🧪 Testing vacature_from_url met ID 115...")
    result = test_vacature_from_url(115)
    
    if result:
        print("\n✅ Test succesvol!")
        print(f"Resultaat: {result}")
    else:
        print("\n❌ Test gefaald!")

if __name__ == "__main__":
    main()
