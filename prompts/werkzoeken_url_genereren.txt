Je krijgt hieronder de HTML-inhoud van een pagina (bijv. een vacaturetekst). Analyseer deze inhoud en haal de relevante gegevens eruit die nodig zijn om een zoek-URL te genereren voor de Werkzoeken.nl CV-database.

📌 Gebruik de Boolean Search-functionaliteit van Werkzoeken.nl zoals uitgelegd op:
https://www.werkzoeken.nl/support/156-hoe-werkt-zoeken-met-boolean-search/

Boolean Search regels:
- Gebruik `AND` alleen als je meerdere **verplichte** termen wilt combineren
- Gebruik `OR` voor **alternatieve benamingen, verwante begrippen of synoniemen**
- Gebruik `NOT` om ongewenste termen uit te sluiten
- Gebruik haakjes `()` om logische groepen te maken
- Gebruik aanhalingstekens `"` om exacte woordgroepen te matchen
- Denk breed: zoektermen zijn bedoeld om **kandidaten te vinden met mogelijke raakvlakken**, niet exacte kopieën van de functieomschrijving

🎯 Instructie voor `what=`:
- De functietitel ("Design Engineer") is de kern. Voeg bij voorkeur gerelateerde inhoud toe uit de functiebeschrijving (zoals "elektrische systemen", "3D ontwerpen", "technische berekeningen") met `OR`, **niet met AND**.
- Vermijd overmatig gebruik van `AND` — het beperkt het zoekresultaat te veel.
- Gebruik `"Design Engineer" OR (...)` in plaats van `"Design Engineer" AND (...)` tenzij het echt om verplichte inhoud gaat.
- Combineer meerdere relevante termen in een `what=` string die breed matcht.

📌 Parameters en betekenis:

- `what=` → de zoektermen (Boolean-formule, correct URL-gecodeerd)
- `where=` → plaatsnaam
- `r=` → straal in kilometers (bv. 20)
- `date=` → periode in dagen (1, 7, 30, 183, 0)
- `hours[]=` → werkuren (1=37-40, 2=32-36, 3=24-32, 4=<24)
- `level[]=` → opleidingsniveau (1=VMBO, 2=MBO, 3=HBO, 4=WO, 5=Onbekend)
- `lang[]=` → taal (1=Nederlands, 2=Engels)
- `dienstverband[]=` → dienstverband (1=Vast, 2=Tijdelijk, 3=Freelance/ZZP)
- `ervaring[]=` → ervaringsniveau (1=Starter, 2=Medior, 3=Senior)
- `license[]=` → rijbewijs (1=Ja, 2=Nee)

🔒 Belangrijk:
- Geef **uitsluitend** de uiteindelijke, correcte URL terug als output.
- **GEEN uitleg**, **GEEN code**, **GEEN `<think>`-output**.
- Alle interne afwegingen doe je stil. Output = 1 regel: de werkende URL.
- Zorg dat je correcte URL-encoding toepast (zoals `%28` voor `(`, `+` voor spaties, enz.)
- Gebruik verplichte velden: `filtered=1`, `sort=0`, `search_type=1`, `viewedvacs=`, `account=`

📎 Voorbeeld van een goede Boolean `what=` zoekstring:
`"Design Engineer" OR (elektrische systemen OR "3D ontwerpen" OR "technische berekeningen")`

Geencodeerd als:
`%22Design+Engineer%22+OR+%28elektrische+systemen+OR+%223D+ontwerpen%22+OR+%22technische+berekeningen%22%29`

📎 Voorbeeld eindresultaat:
https://www.werkzoeken.nl/cv-database/?filtered=1&sort=0&what=%22Design+Engineer%22+OR+%28elektrische+systemen+OR+%223D+ontwerpen%22+OR+%22technische+berekeningen%22%29&r=20&where=Oud-Beijerland&search_type=1&account=&date=183&viewedvacs=&hours%5B%5D=1&level%5B%5D=2&lang%5B%5D=1&dienstverband%5B%5D=1
