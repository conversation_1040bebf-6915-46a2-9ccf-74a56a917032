Je krijgt HTML van een kandidatenprofiel, afkomstig van de website Werkzoeken.nl. Analyseer de HTML en genereer een gestructureerd, vacature-onafhankelijk profiel van de kandidaat in natuurlijke taal. De bedoeling is dat dit profiel gebruikt kan worden om later automatisch gematcht te worden met vacatures.

⚙️ DOEL: een gestandaardiseerde, zakelijke samenvatting van de kandidaat.

🎯 OUTPUTSTRUCTUUR (exact aanhouden):

---

**Naam & Locatie**  
[Voornaam], woonachtig in [woonplaats].

**Beschikbaarheid**  
[Direct / per datum] beschikbaar voor [aantal] uur per week. Contractvoorkeur: [vast / tijdelijk / freelance].

**Ervaringsniveau & Werkervaring**  
- Niveau: [Junior / Medior / Senior]  
- Totaal aantal jaar ervaring: [X jaar]  
- Relevante functies:  
  - [Functietitel 1], [X jaar]  
  - [Functietitel 2], [X jaar]  
  - ...  
- Laatste functie: [Functietitel] sinds [datum].

**Opleiding & Certificaten**  
- Opleidingsniveau: [MBO / HBO / WO]  
- Certificaten: [VCA, softwarecertificaten, etc.]  
- Talen: [Taal 1, Taal 2, etc.]

**Technische en softwarevaardigheden**  
(Benoem bekende pakketten zoals AutoCAD, Cadmatic, Solidworks, Office, ERP-software etc.)

**Rijbewijzen & Mobiliteit**  
- Rijbewijs: [B / C / onbekend]  
- Max. reisafstand: [X km]  
- Beschikt over eigen vervoer: [ja/nee/onbekend]

**Gewenste functies & voorkeuren**  
- Gewenste functies: [functielijst]  
- Contactvoorkeur: [telefoon, e-mail, whatsapp]  
- Werktype: [in loondienst, zzp, hybride, etc.]

**Persoonlijke competenties (indien beschikbaar)**  
[Bijv. stressbestendig, oplossingsgericht, nauwkeurig, teamspeler, communicatief vaardig]

---

📌 Let op:
- Vul “onbekend” in als een gegeven ontbreekt.
- Gebruik uitsluitend informatie uit het HTML-profiel.
- Vermijd herhaling of vage zinnen.
- Houd het zakelijk en bondig (maximaal 200-250 woorden).
