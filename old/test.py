import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto("https://www.werkzoeken.nl/inloggen/")
    page.get_by_role("button", name="Accepteren").click()
    page.get_by_role("textbox", name="E-mailadres").click()
    page.get_by_role("textbox", name="E-mailadres").fill("<EMAIL>")
    page.get_by_role("textbox", name="E-mailadres").press("Tab")
    page.get_by_role("textbox", name="Wachtwoord").fill("Talenten14")
    page.get_by_role("button", name="Inloggen").click()
    page.get_by_role("button", name="CV-database").click()
    page.get_by_role("link", name="<PERSON><PERSON>'s").click()
    page.locator("#what").click()
    page.locator("#what").fill("elec")
    page.locator("a").filter(has_text="electrical engineer").click()
    page.locator("#where_input").click()
    page.locator("#where_input").fill("rotterdam")
    page.get_by_role("listitem").filter(has_text=re.compile(r"^Rotterdam$")).locator("a").click()
    page.locator(".icon.icon-angle-down").first.click()
    page.get_by_role("listitem").filter(has_text="+5 km").click()
    page.get_by_text("Opleiding Geen diploma LBO /").click()
    page.locator("div").filter(has_text=re.compile(r"^MBO$")).click()
    page.get_by_text("Zoek CV's").nth(2).click()
    page.get_by_text("Ervaring Junior Medior Senior").click()
    page.locator("div").filter(has_text=re.compile(r"^Medior$")).click()
    page.get_by_text("Zoek CV's").nth(2).click()
    page.get_by_role("link", name="Janayro • Lang niet actief").click()
    page.get_by_role("link", name="D Dawod • Lang niet actief").click()
    page.locator("a").filter(has_text="Sakina").click()
    page.get_by_role("link", name="Uitloggen").click()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
