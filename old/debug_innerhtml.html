<!DOCTYPE html><html lang="en" style="" class=" no-hairline"><head>
    <meta charset="UTF-8">
    <title>Antibot</title>

    <style>
        iframe.canvased {
            display: none;
        }

        table {
            border: 3px solid black;
            padding: 0;
            margin: 0 0 10px 0;
        }

        tr, td {
            padding: 5px;
            margin: 0;
        }

        td {
            border: 1px solid #CCC;
        }

        td.passed {
            background-color: #c8d86d;
            max-width: 300px;
            word-wrap: break-word;
        }

        td.warn {
            background-color: #ffed53;
            max-width: 300px;
            word-wrap: break-word;
        }

        td.failed {
            background-color: #f45159;
        }

        span.age {
            float: right;
            margin-left: 40px;
            margin-right: 10px;
        }
    </style>
    <script async="" src="https://mc.yandex.ru/metrika/tag.js"></script><script src="./fpCollect.min.js"></script>
    <script src="./modernizr.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.11/lodash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ua-parser-js@0.7.19/src/ua-parser.min.js"></script>
    <script src="./fpScanner.js"></script>
</head>
<body>
<!-- Yandex.Metrika counter -->
<script type="text/javascript">
    (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
        m[i].l=1*new Date();k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
    (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");

    ym(86860295, "init", {
        clickmap:true,
        trackLinks:true,
        accurateTrackBounce:true
    });
</script>
<noscript><div><img src="https://mc.yandex.ru/watch/86860295" style="position:absolute; left:-9999px;" alt="" /></div></noscript>
<!-- /Yandex.Metrika counter -->

<h1>Intoli.com tests + additions</h1>
<table>
    <tbody><tr>
        <th>Test Name</th>
        <th>Result</th>
    </tr>
    <tr>
        <td>User Agent <span class="age">(Old)</span></td>
        <td class="result passed" id="user-agent-result">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</td>
    </tr>
    <tr>
        <td>WebDriver <span class="age">(New)</span></td>
        <td class="result passed" id="webdriver-result">missing (passed)</td>
    </tr>
    <tr>
        <td>WebDriver Advanced</td>
        <td class="result passed" id="advanced-webdriver-result">passed</td>
    </tr>
    <tr>
        <td>Chrome <span class="age">(New)</span></td>
        <td class="result passed" id="chrome-result">present (passed)</td>
    </tr>
    <tr>
        <td>Permissions <span class="age">(New)</span></td>
        <td class="result passed" id="permissions-result">denied</td>
    </tr>
    <tr>
        <td>Plugins Length <span class="age">(Old)</span></td>
        <td class="result passed" id="plugins-length-result">5</td>
    </tr>
    <tr>
        <td>Plugins is of type PluginArray</td>
        <td class="result passed" id="plugins-type-result">passed</td>
    </tr>
    <tr>
        <td>Languages <span class="age">(Old)</span></td>
        <td class="result passed" id="languages-result">nl-NL,nl,en-US,en</td>
    </tr>
    <tr>
        <td>WebGL Vendor</td>
        <td id="webgl-vendor" class="passed">Google Inc. (NVIDIA Corporation)</td>
    </tr>
    <tr>
        <td>WebGL Renderer</td>
        <td id="webgl-renderer" class="passed">ANGLE (NVIDIA Corporation, NVIDIA GeForce GTX 1650 Ti with Max-Q Design/PCIe/SSE2, OpenGL 4.5.0)</td>
    </tr>
    <tr>
        <td>Broken Image Dimensions</td>
        <td id="broken-image-dimensions" class="passed">16x16</td>
    </tr>
</tbody></table>

<h1><a href="https://github.com/antoinevastel/fpscanner" target="_blank">Fingerprint Scanner</a> tests</h1>
<table id="fp2"><tr><td>PHANTOM_UA</td><td class="passed">ok</td><td><pre>{
     "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}</pre></td></tr><tr><td>PHANTOM_PROPERTIES</td><td class="passed">ok</td><td><pre>{
     "attributesFound": [
          false,
          false,
          false
     ]
}</pre></td></tr><tr><td>PHANTOM_ETSL</td><td class="passed">ok</td><td><pre>{
     "etsl": 33
}</pre></td></tr><tr><td>PHANTOM_LANGUAGE</td><td class="passed">ok</td><td><pre>{
     "languages": [
          "nl-NL",
          "nl",
          "en-US",
          "en"
     ]
}</pre></td></tr><tr><td>PHANTOM_WEBSOCKET</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>MQ_SCREEN</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>PHANTOM_OVERFLOW</td><td class="passed">ok</td><td><pre>{
     "depth": 9593,
     "errorMessage": "Maximum call stack size exceeded",
     "errorName": "RangeError",
     "errorStacklength": 846
}</pre></td></tr><tr><td>PHANTOM_WINDOW_HEIGHT</td><td class="passed">ok</td><td><pre>{
     "wInnerHeight": 768,
     "wOuterHeight": 768,
     "wOuterWidth": 1366,
     "wInnerWidth": 1366,
     "wScreenX": 0,
     "wPageXOffset": 0,
     "wPageYOffset": 0,
     "cWidth": 1350,
     "cHeight": 1669,
     "sWidth": 1366,
     "sHeight": 768,
     "sAvailWidth": 1366,
     "sAvailHeight": 768,
     "sColorDepth": 24,
     "sPixelDepth": 24,
     "wDevicePixelRatio": 1
}</pre></td></tr><tr><td>HEADCHR_UA</td><td class="passed">ok</td><td><pre>{
     "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}</pre></td></tr><tr><td>HEADCHR_CHROME_OBJ</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>HEADCHR_PERMISSIONS</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>HEADCHR_PLUGINS</td><td class="passed">ok</td><td><pre>{
     "plugins": [
          "Chrome PDF Plugin::Portable Document Format::internal-pdf-viewer::__~~,~~,~~,~~",
          "Chrome PDF Viewer::::mhjfbmdgcfjbbpaeojofohoefgiehjai::__~~,~~,~~,~~",
          "Native Client::::internal-nacl-plugin::__~~,~~,~~,~~",
          "WebKit built-in PDF::Portable Document Format::webkit-pdf-plugin::__~~,~~,~~,~~",
          "Microsoft Edge PDF Viewer::Portable Document Format::edge-pdf-viewer::__~~,~~,~~,~~"
     ]
}</pre></td></tr><tr><td>HEADCHR_IFRAME</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>CHR_DEBUG_TOOLS</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>SELENIUM_DRIVER</td><td class="passed">ok</td><td><pre>{
     "attributesFound": [
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false
     ]
}</pre></td></tr><tr><td>CHR_BATTERY</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>CHR_MEMORY</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>TRANSPARENT_PIXEL</td><td class="passed">ok</td><td><pre>{
     "0": 0,
     "1": 0,
     "2": 0,
     "3": 0
}</pre></td></tr><tr><td>SEQUENTUM</td><td class="passed">ok</td><td><pre>{}</pre></td></tr><tr><td>VIDEO_CODECS</td><td class="passed">ok</td><td><pre>{
     "h264": "probably"
}</pre></td></tr></table>

<h1>Some details</h1>
<table>
    <tbody><tr>
        <td>navigator.cookieEnabled</td>
        <td>
            <script>
              document.write(navigator.cookieEnabled);
            </script>true
        </td>
    </tr>
    <tr>
        <td>navigator.doNotTrack</td>
        <td>
            <script>
              document.write(navigator.doNotTrack);
            </script>null
        </td>
    </tr>
    <tr>
        <td>navigator.msDoNotTrack</td>
        <td>
            <script>
              document.write(navigator.msDoNotTrack);
            </script>undefined
        </td>
    </tr>
    <tr>
        <td>navigator.sendBeacon</td>
        <td>
            <script>
              document.write(navigator.sendBeacon());
            </script>
        </td>
    </tr>
    <tr>
        <td>navigator.cookieEnabled</td>
        <td>
            <script>
              document.write(navigator.cookieEnabled);
            </script>true
        </td>
    </tr>
    <tr>
        <td>navigator.userAgent</td>
        <td>
            <script>
              document.write(navigator.userAgent);
            </script>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
        </td>
    </tr>
    <tr>
        <td>navigator.appName</td>
        <td>
            <script>
              document.write(navigator.appName);
            </script>Netscape
        </td>
    </tr>
    <tr>
        <td>navigator.vendor</td>
        <td>
            <script>
              document.write(navigator.vendor);
            </script>Google Inc.
        </td>
    </tr>
    <tr>
        <td>navigator.appCodeName</td>
        <td>
            <script>
              document.write(navigator.appCodeName);
            </script>Mozilla
        </td>
    </tr>
    <tr>
        <td>navigator.getUserMedia</td>
        <td>
            <script>
              document.write(navigator.getUserMedia());
            </script>
        </td>
    </tr>
    <tr>
        <td>navigator.sayswho</td>
        <td>
            <script>
              document.write(navigator.sayswho);
            </script>undefined
        </td>
    </tr>
    <tr>
        <td>navigator.javaEnabled</td>
        <td>
            <script>
              document.write(navigator.javaEnabled());
            </script>false
        </td>
    </tr>
    <tr>
        <td>navigator.plugins</td>
        <td>
            <script>
              document.write(JSON.stringify(navigator.plugins));
            </script>[{"name":"Chrome PDF Plugin","filename":"internal-pdf-viewer","description":"Portable Document Format"},{"name":"Chrome PDF Viewer","filename":"mhjfbmdgcfjbbpaeojofohoefgiehjai","description":""},{"name":"Native Client","filename":"internal-nacl-plugin","description":""},{"name":"WebKit built-in PDF","filename":"webkit-pdf-plugin","description":"Portable Document Format"},{"name":"Microsoft Edge PDF Viewer","filename":"edge-pdf-viewer","description":"Portable Document Format"}]
        </td>
    </tr>
    <tr>
        <td>screen.width</td>
        <td>
            <script>
              document.write(screen.width);
            </script>1366
        </td>
    </tr>
    <tr>
        <td>screen.height</td>
        <td>
            <script>
              document.write(screen.height);
            </script>768
        </td>
    </tr>
    <tr>
        <td>screen.colorDepth</td>
        <td>
            <script>
              document.write(screen.colorDepth);
            </script>24
        </td>
    </tr>
    <tr>
        <td>navigator.language</td>
        <td>
            <script>
              document.write(navigator.language);
            </script>nl-NL
        </td>
    </tr>
    <tr>
        <td>navigator.loadPurpose</td>
        <td>
            <script>
              document.write(navigator.loadPurpose);
            </script>undefined
        </td>
    </tr>
    <tr>
        <td>navigator.platform</td>
        <td>
            <script>
              document.write(navigator.platform);
            </script>Linux x86_64
        </td>
    </tr>
    <tr>
        <td>navigator.mediaDevices</td>
        <td id="media1">audioinput:  id = <br>videoinput:  id = <br>audiooutput:  id = <br></td>
    </tr>
    <tr>
        <td>navigator.getBattery details</td>
        <td id="battery1">Charging: true<br>Level: 0.99</td>
    </tr>
    <tr>
        <td>Canvas1</td>
        <td id="canvas1"><canvas width="220" height="30"></canvas><div>Hash: -664801646</div></td>
    </tr>
    <tr>
        <td>Canvas2</td>
        <td id="canvas2"><canvas width="220" height="30"></canvas><div>Hash: -664801646</div></td>
    </tr>
    <tr>
        <td>Canvas3 (iframe sandbox)</td>
        <td id="canvas3">
            <iframe class="canvased" id="canvas3-iframe" sandbox="allow-same-origin"></iframe>
        <canvas width="220" height="30"></canvas><div>Hash: -664801646</div></td>
    </tr>
    <tr>
        <td>Canvas4 (iframe sandbox)</td>
        <td id="canvas4">
            <iframe class="canvased" id="canvas4-iframe" sandbox="allow-same-origin"></iframe>
        <canvas width="220" height="30"></canvas><div>Hash: -664801646</div></td>
    </tr>
    <tr>
        <td>Canvas5 (iframe)</td>
        <td id="canvas5">
            <iframe class="canvased" id="canvas5-iframe"></iframe>
        <canvas width="220" height="30"></canvas><div>Hash: -664801646</div></td>
    </tr>
</tbody></table>

<h1>Fp-collect info</h1>
<div style="padding:10px; border:1px solid red;">
    <pre id="fp">{
     "plugins": [
          "Chrome PDF Plugin::Portable Document Format::internal-pdf-viewer::__~~,~~,~~,~~",
          "Chrome PDF Viewer::::mhjfbmdgcfjbbpaeojofohoefgiehjai::__~~,~~,~~,~~",
          "Native Client::::internal-nacl-plugin::__~~,~~,~~,~~",
          "WebKit built-in PDF::Portable Document Format::webkit-pdf-plugin::__~~,~~,~~,~~",
          "Microsoft Edge PDF Viewer::Portable Document Format::edge-pdf-viewer::__~~,~~,~~,~~"
     ],
     "mimeTypes": [],
     "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
     "platform": "Linux x86_64",
     "languages": [
          "nl-NL",
          "nl",
          "en-US",
          "en"
     ],
     "screen": {
          "wInnerHeight": 768,
          "wOuterHeight": 768,
          "wOuterWidth": 1366,
          "wInnerWidth": 1366,
          "wScreenX": 0,
          "wPageXOffset": 0,
          "wPageYOffset": 0,
          "cWidth": 1350,
          "cHeight": 1669,
          "sWidth": 1366,
          "sHeight": 768,
          "sAvailWidth": 1366,
          "sAvailHeight": 768,
          "sColorDepth": 24,
          "sPixelDepth": 24,
          "wDevicePixelRatio": 1
     },
     "touchScreen": [
          0,
          false,
          false
     ],
     "videoCard": [
          "Google Inc. (NVIDIA Corporation)",
          "ANGLE (NVIDIA Corporation, NVIDIA GeForce GTX 1650 Ti with Max-Q Design/PCIe/SSE2, OpenGL 4.5.0)"
     ],
     "multimediaDevices": {
          "speakers": 1,
          "micros": 1,
          "webcams": 1
     },
     "productSub": "20030107",
     "navigatorPrototype": [
          "plugins~~~function get plugins() { [native code] }",
          "languages~~~function get languages() { [native code] }",
          "vendorSub~~~function get vendorSub() { [native code] }",
          "productSub~~~function get productSub() { [native code] }",
          "vendor~~~function get vendor() { [native code] }",
          "maxTouchPoints~~~function get maxTouchPoints() { [native code] }",
          "scheduling~~~function get scheduling() { [native code] }",
          "userActivation~~~function get userActivation() { [native code] }",
          "doNotTrack~~~function get doNotTrack() { [native code] }",
          "geolocation~~~function get geolocation() { [native code] }",
          "connection~~~function get connection() { [native code] }",
          "plugins~~~function get plugins() { [native code] }",
          "mimeTypes~~~function get mimeTypes() { [native code] }",
          "pdfViewerEnabled~~~function get pdfViewerEnabled() { [native code] }",
          "webkitTemporaryStorage~~~function get webkitTemporaryStorage() { [native code] }",
          "webkitPersistentStorage~~~function get webkitPersistentStorage() { [native code] }",
          "windowControlsOverlay~~~function get windowControlsOverlay() { [native code] }",
          "hardwareConcurrency~~~function get hardwareConcurrency() { [native code] }",
          "cookieEnabled~~~function get cookieEnabled() { [native code] }",
          "appCodeName~~~function get appCodeName() { [native code] }",
          "appName~~~function get appName() { [native code] }",
          "appVersion~~~function get appVersion() { [native code] }",
          "platform~~~function get platform() { [native code] }",
          "product~~~function get product() { [native code] }",
          "userAgent~~~function get userAgent() { [native code] }",
          "language~~~function get language() { [native code] }",
          "languages~~~function get languages() { [native code] }",
          "onLine~~~function get onLine() { [native code] }",
          "getGamepads~~~function getGamepads() { [native code] }",
          "javaEnabled~~~function javaEnabled() { [native code] }",
          "sendBeacon~~~function sendBeacon() { [native code] }",
          "vibrate~~~function vibrate() { [native code] }",
          "constructor~~~function Navigator() { [native code] }",
          "deprecatedRunAdAuctionEnforcesKAnonymity~~~function get deprecatedRunAdAuctionEnforcesKAnonymity() { [native code] }",
          "protectedAudience~~~function get protectedAudience() { [native code] }",
          "storageBuckets~~~function get storageBuckets() { [native code] }",
          "clipboard~~~function get clipboard() { [native code] }",
          "credentials~~~function get credentials() { [native code] }",
          "keyboard~~~function get keyboard() { [native code] }",
          "managed~~~function get managed() { [native code] }",
          "mediaDevices~~~function get mediaDevices() { [native code] }",
          "storage~~~function get storage() { [native code] }",
          "serviceWorker~~~function get serviceWorker() { [native code] }",
          "virtualKeyboard~~~function get virtualKeyboard() { [native code] }",
          "wakeLock~~~function get wakeLock() { [native code] }",
          "deviceMemory~~~function get deviceMemory() { [native code] }",
          "userAgentData~~~function get userAgentData() { [native code] }",
          "login~~~function get login() { [native code] }",
          "ink~~~function get ink() { [native code] }",
          "mediaCapabilities~~~function get mediaCapabilities() { [native code] }",
          "devicePosture~~~function get devicePosture() { [native code] }",
          "hid~~~function get hid() { [native code] }",
          "locks~~~function get locks() { [native code] }",
          "gpu~~~function get gpu() { [native code] }",
          "mediaSession~~~function get mediaSession() { [native code] }",
          "permissions~~~function get permissions() { [native code] }",
          "presentation~~~function get presentation() { [native code] }",
          "serial~~~function get serial() { [native code] }",
          "usb~~~function get usb() { [native code] }",
          "xr~~~function get xr() { [native code] }",
          "adAuctionComponents~~~function adAuctionComponents() { [native code] }",
          "runAdAuction~~~function runAdAuction() { [native code] }",
          "canLoadAdAuctionFencedFrame~~~function canLoadAdAuctionFencedFrame() { [native code] }",
          "clearAppBadge~~~function clearAppBadge() { [native code] }",
          "getBattery~~~function getBattery() { [native code] }",
          "getUserMedia~~~function getUserMedia() { [native code] }",
          "requestMIDIAccess~~~function requestMIDIAccess() { [native code] }",
          "requestMediaKeySystemAccess~~~function requestMediaKeySystemAccess() { [native code] }",
          "setAppBadge~~~function setAppBadge() { [native code] }",
          "webkitGetUserMedia~~~function webkitGetUserMedia() { [native code] }",
          "clearOriginJoinedAdInterestGroups~~~function clearOriginJoinedAdInterestGroups() { [native code] }",
          "createAuctionNonce~~~function createAuctionNonce() { [native code] }",
          "joinAdInterestGroup~~~function joinAdInterestGroup() { [native code] }",
          "leaveAdInterestGroup~~~function leaveAdInterestGroup() { [native code] }",
          "updateAdInterestGroups~~~function updateAdInterestGroups() { [native code] }",
          "deprecatedReplaceInURN~~~function deprecatedReplaceInURN() { [native code] }",
          "deprecatedURNToURL~~~function deprecatedURNToURL() { [native code] }",
          "getInstalledRelatedApps~~~function getInstalledRelatedApps() { [native code] }",
          "getInterestGroupAdAuctionData~~~function getInterestGroupAdAuctionData() { [native code] }",
          "registerProtocolHandler~~~function registerProtocolHandler() { [native code] }",
          "unregisterProtocolHandler~~~function unregisterProtocolHandler() { [native code] }",
          "constructor~~~function Navigator() { [native code] }",
          "__defineGetter__~~~",
          "__defineSetter__~~~",
          "hasOwnProperty~~~",
          "__lookupGetter__~~~",
          "__lookupSetter__~~~",
          "isPrototypeOf~~~",
          "propertyIsEnumerable~~~",
          "toString~~~",
          "valueOf~~~",
          "__proto__~~~",
          "toLocaleString~~~"
     ],
     "etsl": 33,
     "screenDesc": "function get width() { [native code] }",
     "phantomJS": [
          false,
          false,
          false
     ],
     "nightmareJS": false,
     "selenium": [
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false
     ],
     "webDriver": false,
     "errorsGenerated": [
          "azeaze is not defined",
          null,
          null,
          null,
          null,
          null,
          null
     ],
     "resOverflow": {
          "depth": 9593,
          "errorMessage": "Maximum call stack size exceeded",
          "errorName": "RangeError",
          "errorStacklength": 846
     },
     "accelerometerUsed": false,
     "screenMediaQuery": true,
     "hasChrome": true,
     "detailChrome": {
          "webstore": "TypeError: Cannot read properties of undefined (reading 'constructor')",
          "runtime": "function Object() { [native code] }",
          "app": "TypeError: Cannot read properties of undefined (reading 'constructor')",
          "csi": "function Function() { [native code] }",
          "loadTimes": "function Function() { [native code] }",
          "connect": "window.chrome.runtime.connect is not a function",
          "sendMessage": "window.chrome.runtime.sendMessage is not a function"
     },
     "permissions": {
          "state": "denied",
          "permission": "denied"
     },
     "iframeChrome": "object",
     "debugTool": false,
     "battery": true,
     "deviceMemory": 8,
     "tpCanvas": {
          "0": 0,
          "1": 0,
          "2": 0,
          "3": 0
     },
     "sequentum": false,
     "audioCodecs": {
          "ogg": "probably",
          "mp3": "probably",
          "wav": "probably",
          "m4a": "probably",
          "aac": "probably"
     },
     "videoCodecs": {
          "ogg": "maybe",
          "h264": "probably",
          "webm": "probably"
     }
}</pre>
</div>

<script>
  fpCollect.generateFingerprint().then((fingerprint) => {
    $("#fp").text(JSON.stringify(fingerprint, null, 5));

    let xscanner = fpscanner;
    scannerResults = xscanner.analyseFingerprint(fingerprint);

    for (name in scannerResults) {
      data = scannerResults[name];
      let ok = "FAIL";
      let c = "failed";
      if (data.consistent == 2) {
        ok = "WARN";
        c = "warn";
      }
      if (data.consistent == 3) {
        ok = "ok";
        c = "passed";
      }
      $("#fp2").append("<tr><td>" + name + "</td><td class='" + c + "'>" + ok + "</td><td><pre>" + JSON.stringify(data.data, null, 5) + "</pre></td></tr>");
    }
  });
</script>

<script>
  function utf8_encode(str_data) {	// Encodes an ISO-8859-1 string to UTF-8
    //
    // +   original by: Webtoolkit.info (http://www.webtoolkit.info/)

    str_data = str_data.replace(/\r\n/g, "\n");
    var utftext = "";

    for (var n = 0; n < str_data.length; n++) {
      var c = str_data.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if (( c > 127 ) && ( c < 2048 )) {
        utftext += String.fromCharCode(( c >> 6 ) | 192);
        utftext += String.fromCharCode(( c & 63 ) | 128);
      } else {
        utftext += String.fromCharCode(( c >> 12 ) | 224);
        utftext += String.fromCharCode(( ( c >> 6 ) & 63 ) | 128);
        utftext += String.fromCharCode(( c & 63 ) | 128);
      }
    }

    return utftext;
  }


  function crc32(str) {	// Calculates the crc32 polynomial of a string
    //
    // +   original by: Webtoolkit.info (http://www.webtoolkit.info/)

    str = utf8_encode(str);
    var table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

    if (typeof ( crc ) == "undefined") {
      crc = 0;
    }
    var x = 0;
    var y = 0;

    crc = crc ^ ( -1 );
    for (var i = 0, iTop = str.length; i < iTop; i++) {
      y = ( crc ^ str.charCodeAt(i) ) & 0xFF;
      x = "0x" + table.substr(y * 9, 8);
      crc = ( crc >>> 8 ) ^ x;
    }

    return crc ^ ( -1 );
  }

  String.prototype.hashCode = function () {
    var hash = 0, i, chr;
    if (this.length === 0) return hash;
    for (i = 0; i < this.length; i++) {
      chr = this.charCodeAt(i);
      hash = ( ( hash << 5 ) - hash ) + chr;
      hash |= 0; // Convert to 32bit integer
    }
    return hash;
  };
</script>

<script>
  runBotDetection = function () {
    var documentDetectionKeys = [
      "__webdriver_evaluate",
      "__selenium_evaluate",
      "__webdriver_script_function",
      "__webdriver_script_func",
      "__webdriver_script_fn",
      "__fxdriver_evaluate",
      "__driver_unwrapped",
      "__webdriver_unwrapped",
      "__driver_evaluate",
      "__selenium_unwrapped",
      "__fxdriver_unwrapped",
      "webdriver",
      "__driver_evaluate",
      "__webdriver_evaluate",
      "__selenium_evaluate",
      "__fxdriver_evaluate",
      "__driver_unwrapped",
      "__webdriver_unwrapped",
      "__selenium_unwrapped",
      "__fxdriver_unwrapped",
      "_Selenium_IDE_Recorder",
      "_selenium",
      "calledSelenium",
      "_WEBDRIVER_ELEM_CACHE",
      "ChromeDriverw",
      "driver-evaluate",
      "webdriver-evaluate",
      "selenium-evaluate",
      "webdriverCommand",
      "webdriver-evaluate-response",
      "__webdriverFunc",
      "__webdriver_script_fn",
      "__$webdriverAsyncExecutor",
      "__lastWatirAlert",
      "__lastWatirConfirm",
      "__lastWatirPrompt",
      "$chrome_asyncScriptInfo",
      "$cdc_asdjflasutopfhvcZLmcfl_"
    ];

    var windowDetectionKeys = [
      "_phantom",
      "__nightmare",
      "_selenium",
      "callPhantom",
      "callSelenium",
      "_Selenium_IDE_Recorder",
    ];

    for (const windowDetectionKey in windowDetectionKeys) {
      const windowDetectionKeyValue = windowDetectionKeys[windowDetectionKey];
      if (window[windowDetectionKeyValue]) {
        return true;
      }
    }
    for (const documentDetectionKey in documentDetectionKeys) {
      const documentDetectionKeyValue = documentDetectionKeys[documentDetectionKey];
      if (window['document'][documentDetectionKeyValue]) {
        return true;
      }
    }

    for (const documentKey in window['document']) {
      if (documentKey.match(/\$[a-z]dc_/) && window['document'][documentKey]['cache_']) {
        return true;
      }
    }

    if (window['external'] && window['external'].toString() && (window['external'].toString()['indexOf']('Sequentum') != -1)) return true;

    if (window['document']['documentElement']['getAttribute']('selenium')) return true;
    if (window['document']['documentElement']['getAttribute']('webdriver')) return true;
    if (window['document']['documentElement']['getAttribute']('driver')) return true;

    return false;
  };

  // User-Agent Test
  const userAgentElement = document.getElementById('user-agent-result');
  userAgentElement.innerHTML = navigator.userAgent;
  if (/HeadlessChrome/.test(navigator.userAgent)) {
    userAgentElement.classList.add('failed');
    userAgentElement.classList.remove('passed');
  } else {
    userAgentElement.classList.add('passed');
    userAgentElement.classList.remove('failed');
  }

  // Webdriver Test
  const webdriverElement = document.getElementById('webdriver-result');
  if (navigator.webdriver || _.has(navigator, "webdriver")) {
    webdriverElement.classList.add('failed');
    webdriverElement.classList.remove('passed');
    webdriverElement.innerHTML = 'present (failed)';
  } else {
    webdriverElement.classList.add('passed');
    webdriverElement.classList.remove('failed');
    webdriverElement.innerHTML = 'missing (passed)';
  }

  // Advanced Webdriver Test
  const webdriverElement2 = document.getElementById('advanced-webdriver-result');
  if (runBotDetection()) {
    webdriverElement2.classList.add('failed');
    webdriverElement2.classList.remove('passed');
    webdriverElement2.innerHTML = 'failed';
  } else {
    webdriverElement2.classList.add('passed');
    webdriverElement2.classList.remove('failed');
    webdriverElement2.innerHTML = 'passed';
  }

  // Chrome Test
  const chromeElement = document.getElementById('chrome-result');
  if (!window.chrome) {
    chromeElement.classList.add('failed');
    chromeElement.classList.remove('passed');
    chromeElement.innerHTML = 'missing (failed)';
  } else {
    chromeElement.classList.add('passed');
    chromeElement.classList.remove('failed');
    chromeElement.innerHTML = 'present (passed)';
  }

  // Permissions Test
  const permissionsElement = document.getElementById('permissions-result');
  ( async () => {
    const permissionStatus = await navigator.permissions.query({ name: 'notifications' });
    permissionsElement.innerHTML = permissionStatus.state;
    if (Notification.permission === 'denied' && permissionStatus.state === 'prompt') {
      permissionsElement.classList.add('failed');
      permissionsElement.classList.remove('passed');
    } else {
      permissionsElement.classList.add('passed');
      permissionsElement.classList.remove('failed');
    }
  } )();

  // Plugins Length Test
  const pluginsLengthElement = document.getElementById('plugins-length-result');
  pluginsLengthElement.innerHTML = navigator.plugins.length;
  if (navigator.plugins.length === 0) {
    pluginsLengthElement.classList.add('failed');
    pluginsLengthElement.classList.remove('passed');
  } else {
    pluginsLengthElement.classList.add('passed');
    pluginsLengthElement.classList.remove('failed');
  }

  // Plugins type Test
  const pluginsTypeElement = document.getElementById('plugins-type-result');
  if (!( navigator.plugins instanceof PluginArray ) || navigator.plugins.length === 0 || window.navigator.plugins[0].toString() !== '[object Plugin]') {
    pluginsTypeElement.classList.add('failed');
    pluginsTypeElement.classList.remove('passed');
    pluginsTypeElement.innerText = "failed";
  } else {
    pluginsTypeElement.classList.add('passed');
    pluginsTypeElement.classList.remove('failed');
    pluginsTypeElement.innerText = "passed";
  }

  // Languages Test
  const languagesElement = document.getElementById('languages-result');
  languagesElement.innerHTML = navigator.languages;
  if (!navigator.languages || navigator.languages.length === 0) {
    languagesElement.classList.add('failed');
    languagesElement.classList.remove('passed');
  } else {
    languagesElement.classList.add('passed');
    languagesElement.classList.remove('failed');
  }

  // WebGL Tests
  const webGLVendorElement = document.getElementById('webgl-vendor');
  const webGLRendererElement = document.getElementById('webgl-renderer');

  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('webgl-experimental');
  if (gl) {
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');

    try {
      // WebGL Vendor Test
      const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
      webGLVendorElement.innerHTML = vendor;
      if (vendor === 'Brian Paul' || vendor === "Google Inc.") {
        webGLVendorElement.classList.add('failed');
      } else {
        webGLVendorElement.classList.add('passed');
      }
    } catch (e) {
      webGLVendorElement.innerHTML = "Error: " + e;
    }

    try {
      // WebGL Renderer Test
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
      webGLRendererElement.innerHTML = renderer;
      if (renderer === 'Mesa OffScreen' || renderer.indexOf("Swift") !== -1) {
        webGLRendererElement.classList.add('failed');
      } else
        webGLRendererElement.classList.add('passed');
    } catch (e) {
      webGLRendererElement.innerHTML = "Error: " + e;
    }
  } else {
    webGLVendorElement.innerHTML = "Canvas has no webgl context";
    webGLRendererElement.innerHTML = "Canvas has no webgl context";
    webGLVendorElement.classList.add('failed');
    webGLRendererElement.classList.add('failed');
  }

  // Broken Image Dimensions Test
  const brokenImageDimensionsElement = document.getElementById('broken-image-dimensions');
  const body = document.body;
  const image = document.createElement('img');
  image.onerror = function () {
    brokenImageDimensionsElement.innerHTML = `${image.width}x${image.height}`;
    if (image.width == 0 && image.height == 0) {
      brokenImageDimensionsElement.classList.add('failed');
    } else {
      brokenImageDimensionsElement.classList.add('passed');
    }
  };
  body.appendChild(image);
  image.src = 'https://intoli.com/nonexistent-image.png';

  let drawCanvas2 = function (num, useIframe = false) {
    var canvas2d;

    /** @type {boolean} */
    var isOkCanvas = true;

    /** @type {string} */
    var canvasText = "Bot test <canvas> 1.1";

    let canvasContainer = document.getElementById("canvas" + num);
    let iframe = document.getElementById("canvas" + num + "-iframe");
    //canvasContainer.appendChild(iframe);

    var canvasElement = useIframe ? iframe.contentDocument.createElement("canvas") : document.createElement("canvas");

    if (canvasElement.getContext) {
      canvas2d = canvasElement.getContext("2d");

      try {
        canvasElement.setAttribute("width", 220);
        canvasElement.setAttribute("height", 30);

        canvas2d.textBaseline = "top";
        canvas2d.font = "14px 'Arial'";
        canvas2d.textBaseline = "alphabetic";
        canvas2d.fillStyle = "#f60";
        canvas2d.fillRect(53, 1, 62, 20);
        canvas2d.fillStyle = "#069";
        canvas2d.fillText(canvasText, 2, 15);
        canvas2d.fillStyle = "rgba(102, 204, 0, 0.7)";
        canvas2d.fillText(canvasText, 4, 17);
      } catch (b) {
        /** @type {!Element} */
        canvasElement = document.createElement("canvas");
        canvas2d = canvasElement.getContext("2d");
        if (void 0 === canvas2d || "function" != typeof canvasElement.getContext("2d").fillText) {
          isOkCanvas = false;
        } else {
          canvasElement.setAttribute("width", 220);
          canvasElement.setAttribute("height", 30);
          /** @type {string} */
          canvas2d.textBaseline = "top";
          /** @type {string} */
          canvas2d.font = "14px 'Arial'";
          /** @type {string} */
          canvas2d.textBaseline = "alphabetic";
          /** @type {string} */
          canvas2d.fillStyle = "#f60";
          canvas2d.fillRect(125, 1, 62, 20);
          /** @type {string} */
          canvas2d.fillStyle = "#069";
          canvas2d.fillText(canvasText, 2, 15);
          /** @type {string} */
          canvas2d.fillStyle = "rgba(102, 204, 0, 0.7)";
          canvas2d.fillText(canvasText, 4, 17);
        }
      }

      if (isOkCanvas && "function" == typeof canvasElement.toDataURL) {
        var datUrl = canvasElement.toDataURL("image/png");
        try {
          if ("boolean" == typeof ( datUrl ) || void 0 === datUrl) {
            throw e;
          }
        } catch (a) {
          /** @type {string} */
          datUrl = "";
        }
        if (0 === datUrl.indexOf("data:image/png")) {

        } else {
          /** @type {boolean} */
          isOkCanvas = false;
        }
      } else {
        /** @type {boolean} */
        isOkCanvas = false;
      }
    } else {
      /** @type {boolean} */
      isOkCanvas = false;
    }


    if (isOkCanvas) {
      let newDiv = document.createElement("div");
      newDiv.innerHTML = "Hash: " + datUrl.hashCode();
      canvasContainer.appendChild(canvasElement);
      canvasContainer.appendChild(newDiv);
    } else {
      let newDiv = document.createElement("div");
      newDiv.innerHTML = "Canvas failed";
      canvasContainer.appendChild(newDiv);
    }
  };

  window.canvasCount = 0;

  drawCanvas2("1");
  drawCanvas2("2");

  drawCanvas2("3", true);
  drawCanvas2("4", true);
  drawCanvas2("5", true);
</script><img src="https://intoli.com/nonexistent-image.png">

<iframe srcdoc="Test1"></iframe>
<iframe srcdoc="Test2" sandbox="allow-same-origin"></iframe>



</body></html>