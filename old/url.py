import requests
import os
import re

def lees_prompt(bestandsnaam: str) -> str:
    """
    Leest de prompt uit een tekstbestand in de prompts-map.
    """
    pad = os.path.join(os.path.dirname(__file__), "prompts", bestandsnaam)
    with open(pad, "r", encoding="utf-8") as f:
        return f.read()
    
def haal_html_op(url):
    response = requests.get(url)
    response.raise_for_status()
    return response.text

def stuur_prompt_naar_groq(prompt, html):
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        raise ValueError("❌ GROQ_API_KEY niet gevonden in .env bestand.")
    endpoint = "https://api.groq.com/openai/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "model": "deepseek-r1-distill-llama-70b",
        "messages": [
            {"role": "user", "content": f"{prompt}\n\n{html}"}
        ],
        "max_tokens": 1400,
        "temperature": 0.2
    }
    response = requests.post(endpoint, headers=headers, json=data)
    response.raise_for_status()
    return response.json()["choices"][0]["message"]["content"]

def strip_think_blocks(text: str) -> str:
    """
    Verwijdert alle <think>...</think> blokken uit de tekst.
    """
    return re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL).strip()

def genereer_werkzoeken_url(vac_id: int) -> str:
    """
    Haalt de vacature op via vacID, stuurt prompt en html naar Groq, en retourneert de werkzoeken.nl zoek-URL.
    """
    vacature_url = f"https://www.talentenscout.com/vacature.php?vacID={vac_id}"
    html = haal_html_op(vacature_url)

    prompt = lees_prompt("werkzoeken_url_genereren.txt")

  

    response = stuur_prompt_naar_groq(prompt, html)
    zoek_url = strip_think_blocks(response).strip()
    print(response)
    return zoek_url


def valideer_werkzoeken_url(url: str) -> bool:
    """
    Valideert of de gegenereerde werkzoeken.nl URL compleet en bruikbaar is.
    Controleert op base-url en enkele verplichte parameters.
    """
    base = "https://www.werkzoeken.nl/cv-database/"
    verplicht = ["filtered=1", "what=", "where=", "r=", "date="]
    if not url.startswith(base):
        return False
    for param in verplicht:
        if param not in url:
            return False
    return True

# Voorbeeld gebruik:
# url = genereer_werkzoeken_url(115)
# print("Gegenereerde URL:", url)

# Gebruik na het genereren:
# zoek_url = genereer_werkzoeken_url(115)
# if not valideer_werkzoeken_url(zoek_url):
#     raise ValueError("❌ De gegenereerde zoek_url is niet compleet of ongeldig.")