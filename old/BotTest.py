from playwright.sync_api import sync_playwright
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
from old.url import genereer_werkzoeken_url, lees_prompt, stuur_prompt_naar_groq
from bs4 import BeautifulSoup



import os
from dotenv import load_dotenv
import time
import random
import sys
from random import randint


from pathlib import Path
from dotenv import load_dotenv

dotenv_path = Path(__file__).parent / ".env"
load_dotenv(dotenv_path=dotenv_path)

email = os.getenv("WERKZOEKEN_EMAIL")
wachtwoord = os.getenv("WERKZOEKEN_WACHTWOORD")


if not email or not wachtwoord:
    raise ValueError("❌ Inloggegevens niet gevonden in .env bestand.")

def wacht_random(min_sec=2.0, max_sec=5.0):
    time.sleep(random.uniform(min_sec, max_sec))





def menselijk_typen(page, selector, tekst):
    for letter in tekst:
        page.type(selector, letter, delay=randint(50, 200))
    wacht_random(0.2, 0.7)


with sync_playwright() as p:
    browser = p.chromium.launch(
        headless=True,
        args=[
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-blink-features=AutomationControlled',
            '--disable-ipc-flooding-protection',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-background-timer-throttling',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-client-side-phishing-detection',
            '--disable-component-extensions-with-background-pages',
            '--disable-default-apps',
            '--disable-dev-shm-usage',
            '--disable-extensions',
            '--disable-features=TranslateUI',
            '--disable-web-security',
            '--no-sandbox',
            '--use-gl=desktop',
            '--enable-webgl',
            '--ignore-gpu-blacklist',
            '--enable-gpu-rasterization'
        ]
    )
    context = browser.new_context(
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        locale="nl-NL",
        viewport={"width": 1366, "height": 768},
        device_scale_factor=1,
        is_mobile=False,
        has_touch=False,
        color_scheme="light",
        reduced_motion="no-preference",
        forced_colors="none",
        extra_http_headers={
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "nl-NL,nl;q=0.9,en-US;q=0.8,en;q=0.7",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0"
        }
    )
    page = context.new_page()

    # Console logging voor debugging (uitgeschakeld voor productie)
    # page.on("console", lambda msg: print(f"Console {msg.type}: {msg.text}"))
    # page.on("pageerror", lambda error: print(f"Page error: {error}"))

    # Geavanceerde stealth script - volledig ondetecteerbaar
    page.add_init_script("""
    // 1. WebDriver volledig verwijderen - meerdere methoden
    try {
      // Verwijder uit navigator prototype
      const navigatorProto = Object.getPrototypeOf(navigator);
      if (navigatorProto && 'webdriver' in navigatorProto) {
        delete navigatorProto.webdriver;
      }

      // Verwijder uit navigator zelf
      if ('webdriver' in navigator) {
        delete navigator.webdriver;
      }

      // Verwijder uit window
      if ('webdriver' in window) {
        delete window.webdriver;
      }

      // Zorg ervoor dat de property niet bestaat
      if (Object.hasOwnProperty.call(navigator, 'webdriver')) {
        delete navigator.webdriver;
      }
    } catch(e) {}

    // 2. Chrome object
    try {
      window.chrome = {
        runtime: {},
        loadTimes: function() {},
        csi: function() {}
      };
    } catch(e) {}

    // 3. Plugins - werkende implementatie
    try {
      // Maak fake plugins
      const fakePlugins = [
        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },
        { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' },
        { name: 'WebKit built-in PDF', filename: 'webkit-pdf-plugin', description: 'Portable Document Format' },
        { name: 'Microsoft Edge PDF Viewer', filename: 'edge-pdf-viewer', description: 'Portable Document Format' }
      ];

      // Zorg dat elk plugin object correct toString heeft
      fakePlugins.forEach(plugin => {
        plugin.toString = () => '[object Plugin]';
      });

      // Maak het een echte PluginArray
      fakePlugins.__proto__ = PluginArray.prototype;
      fakePlugins.namedItem = function(name) {
        return this.find(plugin => plugin.name === name) || null;
      };
      fakePlugins.refresh = function() {};

      Object.defineProperty(navigator, 'plugins', {
        get: () => fakePlugins,
        configurable: true
      });
    } catch(e) {}

    // 4. Languages
    try {
      Object.defineProperty(navigator, 'languages', {
        get: () => ['nl-NL', 'nl', 'en-US', 'en']
      });
    } catch(e) {}

    // 5. Permissions
    try {
      const originalQuery = navigator.permissions.query;
      navigator.permissions.query = function(parameters) {
        if (parameters.name === 'notifications') {
          return Promise.resolve({ state: 'denied' });
        }
        return originalQuery.call(this, parameters);
      };

      // Ook Notification.permission instellen
      Object.defineProperty(Notification, 'permission', {
        get: () => 'denied',
        configurable: true
      });
    } catch(e) {}

    // 6. WebGL spoofing
    try {
      const getParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) return 'Google Inc. (NVIDIA Corporation)';
        if (parameter === 37446) return 'ANGLE (NVIDIA Corporation, NVIDIA GeForce GTX 1650 Ti with Max-Q Design/PCIe/SSE2, OpenGL 4.5.0)';
        return getParameter.call(this, parameter);
      };
    } catch(e) {}

    // 7. Video codec spoofing
    try {
      const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
      HTMLVideoElement.prototype.canPlayType = function(type) {
        if (type.includes('h264') || type.includes('mp4') || type.includes('avc1')) {
          return 'probably';
        }
        if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
          return 'probably';
        }
        if (type.includes('ogg') || type.includes('theora')) {
          return 'maybe';
        }
        return originalCanPlayType.call(this, type);
      };
    } catch(e) {}

    // 8. Audio codec spoofing
    try {
      const originalCanPlayTypeAudio = HTMLAudioElement.prototype.canPlayType;
      HTMLAudioElement.prototype.canPlayType = function(type) {
        if (type.includes('mp3') || type.includes('mpeg')) {
          return 'probably';
        }
        if (type.includes('wav') || type.includes('wave')) {
          return 'probably';
        }
        if (type.includes('ogg') || type.includes('vorbis')) {
          return 'probably';
        }
        if (type.includes('m4a') || type.includes('aac')) {
          return 'probably';
        }
        return originalCanPlayTypeAudio.call(this, type);
      };
    } catch(e) {}
    """)


    try:
        page.goto("https://bot.sannysoft.com/", timeout=15000)
    except PlaywrightTimeoutError:
        print("⏰ Timeout bij laden van de pagina.")

    # Extra script na het laden van de pagina
    page.evaluate("""
    // Verwijder webdriver opnieuw na het laden van de pagina
    try {
      const navigatorProto = Object.getPrototypeOf(navigator);
      if (navigatorProto && 'webdriver' in navigatorProto) {
        delete navigatorProto.webdriver;
      }
      if ('webdriver' in navigator) {
        delete navigator.webdriver;
      }
      if ('webdriver' in window) {
        delete window.webdriver;
      }
    } catch(e) {}
    """)

    #page.goto("https://pixelscan.net/fingerprint-check")
    # Wacht tot de body volledig zichtbaar is
    page.wait_for_selector("body", state="visible")
    wacht_random(1, 2)  # Extra wachten voor volledige rendering

    # Sla de volledige innerHTML op
    with open("debug_innerhtml.html", "w", encoding="utf-8") as f:
        f.write(page.content())

    # Maak een screenshot van de hele pagina
    page.screenshot(path="debug_cookies.png", full_page=True)

    # Controleer op botdetectie in de HTML
    from bs4 import BeautifulSoup

    def check_bot_detection(html_path):
        with open(html_path, encoding="utf-8") as f:
            soup = BeautifulSoup(f, "html.parser")
        text = soup.get_text().lower()
        # Lijst met mogelijke botdetectie-termen
        bot_terms = [
            "bot detected", "robot", "automation", "failed", "present (failed)",
            "captcha", "unusual activity", "access denied", "blocked", "forbidden"
        ]
        for term in bot_terms:
            if term in text:
                print(f"❌ Mogelijk als bot gedetecteerd: '{term}' gevonden.")
                return False
        # Controleer op aanwezigheid van loginformulier
        if soup.find("form") and "inloggen" in text:
            print("✅ Loginformulier gevonden, waarschijnlijk niet als bot geblokkeerd.")
            return True
        print("⚠️ Geen duidelijke indicatie van botdetectie gevonden, controleer handmatig.")
        return None

    check_bot_detection("debug_innerhtml.html")


    # ---------------------
    context.close()
    browser.close()
